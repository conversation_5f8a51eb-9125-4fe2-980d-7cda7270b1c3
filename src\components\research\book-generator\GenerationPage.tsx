import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, ArrowRight, Zap } from "lucide-react";

interface GenerationPageProps {
  onNext: () => void;
  onBack: () => void;
}

export function GenerationPage({ onNext, onBack }: GenerationPageProps) {
  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <div className="p-3 bg-orange-100 rounded-full">
            <Zap className="h-8 w-8 text-orange-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Content Generation</h1>
            <p className="text-lg text-gray-600">Generate chapter content with AI</p>
          </div>
        </div>
      </div>

      {/* Placeholder content */}
      <Card>
        <CardHeader>
          <CardTitle>Chapter Queue</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-gray-500">
            <Zap className="h-16 w-16 mx-auto mb-4 text-gray-300" />
            <p className="text-lg mb-2">Chapter Queue Panel</p>
            <p>This will contain the generation queue with status tracking and batch operations</p>
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack} className="flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Outline
        </Button>
        <Button onClick={onNext} className="flex items-center gap-2">
          Continue to Editor
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
