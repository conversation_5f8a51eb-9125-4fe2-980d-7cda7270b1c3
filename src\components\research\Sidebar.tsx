import { But<PERSON> } from "@/components/ui/button";
import {
  FileText,
  Search,
  BookOpen,
  MessageSquare,
  Settings,
  Plus,
  GraduationCap,
  FolderOpen,
  Star,
  Clock,
  <PERSON><PERSON>,
  <PERSON>rkles,
  Book
} from "lucide-react";
import { ActiveView } from "./ResearchDashboard";

interface SidebarProps {
  activeView: ActiveView;
  onViewChange: (view: ActiveView) => void;
}

export function Sidebar({ activeView, onViewChange }: SidebarProps) {
  const menuItems = [
    { id: "editor" as ActiveView, label: "Editor", icon: FileText },
    { id: "ai-generator" as ActiveView, label: "AI Generator", icon: Sparkles },
    { id: "book-generator" as ActiveView, label: "AI Book Generator", icon: Book },
    { id: "search" as ActiveView, label: "Literature", icon: Search },
    { id: "citations" as ActiveView, label: "Citations", icon: BookOpen },
    { id: "chat" as ActiveView, label: "AI Chat", icon: MessageSquare },
  ];

  const recentDocuments = [
    "Neural Networks in Climate Research",
    "Machine Learning Applications",
    "Research Methodology Review",
    "Data Analysis Framework"
  ];

  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center mb-4">
          <GraduationCap className="h-8 w-8 text-blue-600 mr-2" />
          <span className="text-xl font-bold">PaperGenius</span>
        </div>
        <Button className="w-full" size="sm">
          <Plus className="h-4 w-4 mr-2" />
          New Document
        </Button>
      </div>

      {/* Navigation */}
      <nav className="p-4 space-y-2">
        {menuItems.map((item) => (
          <Button
            key={item.id}
            variant={activeView === item.id ? "secondary" : "ghost"}
            className="w-full justify-start"
            onClick={() => onViewChange(item.id)}
          >
            <item.icon className="h-4 w-4 mr-3" />
            {item.label}
          </Button>
        ))}
      </nav>

      {/* Recent Documents */}
      <div className="p-4 flex-1">
        <h3 className="text-sm font-medium text-gray-500 mb-3 flex items-center">
          <Clock className="h-4 w-4 mr-2" />
          Recent Documents
        </h3>
        <div className="space-y-2">
          {recentDocuments.map((doc, index) => (
            <div
              key={index}
              className="text-sm text-gray-700 hover:text-gray-900 cursor-pointer p-2 rounded hover:bg-gray-50 truncate"
            >
              <FolderOpen className="h-3 w-3 inline mr-2" />
              {doc}
            </div>
          ))}
        </div>
      </div>

      {/* Settings */}
      <div className="p-4 border-t">
        <Button variant="ghost" className="w-full justify-start">
          <Settings className="h-4 w-4 mr-3" />
          Settings
        </Button>
      </div>
    </div>
  );
}
