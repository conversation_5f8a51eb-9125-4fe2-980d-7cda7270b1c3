import { useState, useCallback, useMemo } from 'react';
import { Citation } from '../types';
import { useBookStore } from './useBookStore';

export interface CitationStats {
  total: number;
  resolved: number;
  unresolved: number;
  duplicates: number;
  orphaned: number;
  byChapter: Record<string, number>;
}

export interface CitationGroup {
  key: string;
  citations: Citation[];
  isDuplicate: boolean;
  isResolved: boolean;
}

interface UseCitationGraphProps {
  chapterId?: string;
}

export function useCitationGraph({ chapterId }: UseCitationGraphProps = {}) {
  const { 
    citationGraph, 
    chapters,
    addCitation, 
    updateCitation, 
    deleteCitation,
    mergeCitations,
    linkCitationToChapter,
    unlinkCitationFromChapter
  } = useBookStore();

  const [selectedCitations, setSelectedCitations] = useState<Set<string>>(new Set());
  const [filterMode, setFilterMode] = useState<'all' | 'unresolved' | 'duplicates' | 'orphaned'>('all');
  const [sortBy, setSortBy] = useState<'author' | 'year' | 'usage' | 'title'>('author');

  // Get citations for specific chapter or all citations
  const relevantCitations = useMemo(() => {
    const allCitations = Object.values(citationGraph);
    
    if (chapterId) {
      return allCitations.filter(citation => 
        citation.chapterIds.includes(chapterId)
      );
    }
    
    return allCitations;
  }, [citationGraph, chapterId]);

  // Calculate citation statistics
  const stats = useMemo((): CitationStats => {
    const allCitations = Object.values(citationGraph);
    
    const resolved = allCitations.filter(c => 
      c.title && c.source && c.authors.length > 0
    ).length;
    
    const duplicates = new Set();
    const citationKeys = new Map<string, Citation[]>();
    
    allCitations.forEach(citation => {
      const key = `${citation.authors[0]?.toLowerCase()}_${citation.year}`;
      if (!citationKeys.has(key)) {
        citationKeys.set(key, []);
      }
      citationKeys.get(key)!.push(citation);
    });
    
    citationKeys.forEach(citations => {
      if (citations.length > 1) {
        citations.forEach(c => duplicates.add(c.id));
      }
    });

    const orphaned = allCitations.filter(c => 
      c.chapterIds.length === 0 && c.sectionIds.length === 0
    ).length;

    const byChapter: Record<string, number> = {};
    chapters.forEach(chapter => {
      byChapter[chapter.id] = chapter.chapterCitations.length;
    });

    return {
      total: allCitations.length,
      resolved,
      unresolved: allCitations.length - resolved,
      duplicates: duplicates.size,
      orphaned,
      byChapter
    };
  }, [citationGraph, chapters]);

  // Group citations by similarity for duplicate detection
  const citationGroups = useMemo((): CitationGroup[] => {
    const groups = new Map<string, Citation[]>();
    
    relevantCitations.forEach(citation => {
      // Create a key based on first author and year
      const firstAuthor = citation.authors[0]?.toLowerCase().replace(/[^a-z]/g, '') || 'unknown';
      const key = `${firstAuthor}_${citation.year}`;
      
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(citation);
    });

    return Array.from(groups.entries()).map(([key, citations]) => ({
      key,
      citations: citations.sort((a, b) => a.inTextFormat.localeCompare(b.inTextFormat)),
      isDuplicate: citations.length > 1,
      isResolved: citations.every(c => c.title && c.source && c.authors.length > 0)
    }));
  }, [relevantCitations]);

  // Filter and sort citations
  const filteredCitations = useMemo(() => {
    let filtered = relevantCitations;

    // Apply filters
    switch (filterMode) {
      case 'unresolved':
        filtered = filtered.filter(c => !c.title || !c.source || c.authors.length === 0);
        break;
      case 'duplicates':
        const duplicateIds = new Set();
        citationGroups.forEach(group => {
          if (group.isDuplicate) {
            group.citations.forEach(c => duplicateIds.add(c.id));
          }
        });
        filtered = filtered.filter(c => duplicateIds.has(c.id));
        break;
      case 'orphaned':
        filtered = filtered.filter(c => c.chapterIds.length === 0 && c.sectionIds.length === 0);
        break;
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'author':
          const authorA = a.authors[0] || '';
          const authorB = b.authors[0] || '';
          return authorA.localeCompare(authorB);
        case 'year':
          return b.year - a.year; // Newest first
        case 'usage':
          const usageA = a.chapterIds.length + a.sectionIds.length;
          const usageB = b.chapterIds.length + b.sectionIds.length;
          return usageB - usageA; // Most used first
        case 'title':
          return a.title.localeCompare(b.title);
        default:
          return 0;
      }
    });

    return filtered;
  }, [relevantCitations, filterMode, sortBy, citationGroups]);

  // Citation manipulation functions
  const createCitation = useCallback((citationData: Omit<Citation, 'id'>) => {
    return addCitation(citationData);
  }, [addCitation]);

  const updateCitationData = useCallback((citationId: string, updates: Partial<Citation>) => {
    updateCitation(citationId, updates);
  }, [updateCitation]);

  const removeCitation = useCallback((citationId: string) => {
    deleteCitation(citationId);
    setSelectedCitations(prev => {
      const newSet = new Set(prev);
      newSet.delete(citationId);
      return newSet;
    });
  }, [deleteCitation]);

  const mergeDuplicateCitations = useCallback((primaryId: string, duplicateIds: string[]) => {
    const primary = citationGraph[primaryId];
    if (!primary) return;

    // Merge data from duplicates into primary
    const allChapterIds = new Set(primary.chapterIds);
    const allSectionIds = new Set(primary.sectionIds);
    
    duplicateIds.forEach(duplicateId => {
      const duplicate = citationGraph[duplicateId];
      if (duplicate) {
        duplicate.chapterIds.forEach(id => allChapterIds.add(id));
        duplicate.sectionIds.forEach(id => allSectionIds.add(id));
        
        // Update primary with missing information
        if (!primary.title && duplicate.title) primary.title = duplicate.title;
        if (!primary.source && duplicate.source) primary.source = duplicate.source;
        if (!primary.doi && duplicate.doi) primary.doi = duplicate.doi;
        if (!primary.url && duplicate.url) primary.url = duplicate.url;
        if (!primary.referenceText && duplicate.referenceText) {
          primary.referenceText = duplicate.referenceText;
        }
        
        // Merge authors
        duplicate.authors.forEach(author => {
          if (!primary.authors.includes(author)) {
            primary.authors.push(author);
          }
        });
      }
    });

    // Update primary citation
    updateCitation(primaryId, {
      chapterIds: Array.from(allChapterIds),
      sectionIds: Array.from(allSectionIds),
      authors: primary.authors,
      title: primary.title,
      source: primary.source,
      doi: primary.doi,
      url: primary.url,
      referenceText: primary.referenceText
    });

    // Delete duplicates
    duplicateIds.forEach(id => deleteCitation(id));
  }, [citationGraph, updateCitation, deleteCitation]);

  const resolveCitation = useCallback(async (citationId: string) => {
    const citation = citationGraph[citationId];
    if (!citation) return;

    // TODO: Implement actual citation resolution using Crossref API or similar
    // For now, this is a placeholder that marks the citation as resolved
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock resolved data
      const resolvedData: Partial<Citation> = {
        title: citation.title || `Resolved Title for ${citation.inTextFormat}`,
        source: citation.source || 'Journal of Academic Research',
        doi: citation.doi || `10.1000/resolved.${citation.year}.${Math.random().toString(36).substr(2, 9)}`,
        referenceText: `${citation.authors.join(', ')} (${citation.year}). ${citation.title || 'Resolved Title'}. ${citation.source || 'Journal of Academic Research'}.`
      };
      
      updateCitation(citationId, resolvedData);
      return true;
    } catch (error) {
      console.error('Failed to resolve citation:', error);
      return false;
    }
  }, [citationGraph, updateCitation]);

  const resolveAllCitations = useCallback(async () => {
    const unresolvedCitations = relevantCitations.filter(c => 
      !c.title || !c.source || c.authors.length === 0
    );

    const results = await Promise.allSettled(
      unresolvedCitations.map(c => resolveCitation(c.id))
    );

    const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
    return { total: unresolvedCitations.length, successful };
  }, [relevantCitations, resolveCitation]);

  // Selection functions
  const toggleSelection = useCallback((citationId: string) => {
    setSelectedCitations(prev => {
      const newSet = new Set(prev);
      if (newSet.has(citationId)) {
        newSet.delete(citationId);
      } else {
        newSet.add(citationId);
      }
      return newSet;
    });
  }, []);

  const selectAll = useCallback(() => {
    setSelectedCitations(new Set(filteredCitations.map(c => c.id)));
  }, [filteredCitations]);

  const clearSelection = useCallback(() => {
    setSelectedCitations(new Set());
  }, []);

  const deleteSelected = useCallback(() => {
    selectedCitations.forEach(id => deleteCitation(id));
    setSelectedCitations(new Set());
  }, [selectedCitations, deleteCitation]);

  // Utility functions
  const getCitationUsage = useCallback((citationId: string) => {
    const citation = citationGraph[citationId];
    if (!citation) return { chapters: [], sections: [], totalUsage: 0 };

    const chapterTitles = citation.chapterIds.map(id => {
      const chapter = chapters.find(c => c.id === id);
      return chapter ? chapter.title : 'Unknown Chapter';
    });

    return {
      chapters: chapterTitles,
      sections: citation.sectionIds,
      totalUsage: citation.chapterIds.length + citation.sectionIds.length
    };
  }, [citationGraph, chapters]);

  const exportCitations = useCallback((format: 'bibtex' | 'ris' | 'json' = 'json') => {
    const citations = selectedCitations.size > 0 
      ? Array.from(selectedCitations).map(id => citationGraph[id]).filter(Boolean)
      : filteredCitations;

    switch (format) {
      case 'json':
        return JSON.stringify(citations, null, 2);
      case 'bibtex':
        // TODO: Implement BibTeX export
        return citations.map(c => `@article{${c.id},\n  author={${c.authors.join(' and ')}},\n  title={${c.title}},\n  year={${c.year}}\n}`).join('\n\n');
      case 'ris':
        // TODO: Implement RIS export
        return citations.map(c => `TY  - JOUR\nAU  - ${c.authors.join('\nAU  - ')}\nTI  - ${c.title}\nPY  - ${c.year}\nER  -`).join('\n\n');
      default:
        return JSON.stringify(citations, null, 2);
    }
  }, [selectedCitations, citationGraph, filteredCitations]);

  return {
    // Data
    citations: filteredCitations,
    citationGroups,
    stats,
    selectedCitations,
    filterMode,
    sortBy,
    
    // Citation operations
    createCitation,
    updateCitationData,
    removeCitation,
    mergeDuplicateCitations,
    resolveCitation,
    resolveAllCitations,
    
    // Selection
    toggleSelection,
    selectAll,
    clearSelection,
    deleteSelected,
    
    // Filters and sorting
    setFilterMode,
    setSortBy,
    
    // Utilities
    getCitationUsage,
    exportCitations,
    
    // Chapter linking
    linkCitationToChapter,
    unlinkCitationFromChapter
  };
}
