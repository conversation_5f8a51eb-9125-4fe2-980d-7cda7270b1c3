import { LucideIcon } from "lucide-react";

// Book metadata and structure types
export interface BookMeta {
  title: string;
  subtitle?: string;
  authors: string[];
  audience: string;          // e.g., "Graduate students", "Researchers", "Professionals"
  styleSheet: string;        // e.g., "Academic", "Technical", "Popular science"
  synopsis: string;          // Brief description of the book's purpose and scope
  keywords: string[];
  researchField: string;
  targetLength: number;      // Estimated total pages/words
  isbn?: string;
  publisher?: string;
}

// Outline structure for hierarchical content
export interface OutlineNode {
  id: string;                // nanoid()
  title: string;
  description?: string;
  level: number;             // 1 = section, 2 = subsection
  order: number;
  parentId?: string;         // For subsections
  estimatedWords?: number;
  status: 'draft' | 'approved' | 'locked';
}

// Section content within chapters
export interface SectionDraft {
  id: string;                // nanoid()
  outlineNodeId: string;     // Links to OutlineNode
  content: string;           // Markdown content
  wordCount: number;
  status: 'pending' | 'generating' | 'completed' | 'error' | 'reviewing';
  citations: string[];       // Citation keys found in this section
  figures: FigureReference[];
  lastModified: Date;
  aiMetadata?: {
    model: string;
    temperature: number;
    tokens: number;
    generatedAt: Date;
  };
}

// Chapter state management
export interface ChapterState {
  id: string;                // nanoid()
  title: string;
  abstract: string;          // Brief summary for context in later chapters
  order: number;
  outline: OutlineNode[];    // 2-level hierarchy of sections/subsections
  sections: SectionDraft[];  // Generated content for each outline node
  status: 'draft' | 'in-review' | 'locked';
  chapterCitations: string[]; // Unique citation keys used in this chapter
  estimatedWords: number;
  actualWords: number;
  lastModified: Date;
}

// Global book state
export interface BookState {
  metadata: BookMeta;
  chapters: ChapterState[];
  globalGlossary: Term[];
  citationGraph: Record<string, Citation>; // "(Smith,2024)" → Citation object
  figureRegistry: Record<string, FigureReference>;
  currentStage: BookStage;
  lastSaved: Date;
  version: string;
}

// Book generation workflow stages
export type BookStage = 'input' | 'outline' | 'chapterQueue' | 'editor' | 'export';

// Glossary and terminology management
export interface Term {
  id: string;
  term: string;
  definition: string;
  category?: string;         // e.g., "Technical", "Theoretical", "Methodological"
  firstUsedInChapter?: string; // Chapter ID where first introduced
  relatedTerms: string[];    // IDs of related terms
  sources: string[];         // Citation keys for definition sources
}

// Citation management (extends paper-generator Citation)
export interface Citation {
  id: string;                // Unique ID for the citation
  inTextFormat: string;      // How it appears in text, e.g., "(Smith, 2023)"
  authors: string[];         // List of author names
  year: number;              // Publication year
  title: string;             // Title of the work
  source: string;            // Journal/conference/book name
  doi?: string;              // DOI if available
  url?: string;              // URL if available
  chapterIds: string[];      // IDs of chapters where this citation appears
  sectionIds: string[];      // IDs of sections where this citation appears
  referenceText?: string;    // Full formatted reference text
  category?: 'primary' | 'secondary' | 'background'; // Citation importance
  notes?: string;            // Internal notes about the citation
}

// Figure and media management
export interface FigureReference {
  id: string;                // Auto-generated: "Fig ${chapterIndex}-${figureIndex}"
  chapterId: string;
  sectionId: string;
  caption: string;
  src: string;               // Local path or URL
  alt: string;
  type: 'image' | 'chart' | 'diagram' | 'table';
  width?: number;
  height?: number;
  placement: 'inline' | 'top' | 'bottom' | 'page';
  sources: string[];         // Citation keys for figure sources
}

// AI generation options and metadata
export interface BookAIOptions {
  model: string;
  temperature: number;
  maxTokens: number;
  stopSequences?: string[];
  customInstructions?: string;
}

// Generation queue and status tracking
export interface GenerationQueueItem {
  id: string;
  type: 'outline' | 'section' | 'chapter';
  chapterId: string;
  sectionId?: string;
  status: 'pending' | 'generating' | 'completed' | 'error';
  priority: number;
  estimatedTime?: number;    // Seconds
  startedAt?: Date;
  completedAt?: Date;
  error?: string;
}

// Export configuration
export interface ExportConfig {
  format: 'pdf' | 'epub' | 'latex' | 'docx';
  includeGlossary: boolean;
  includeIndex: boolean;     // Future feature
  referencesMode: 'global' | 'per-chapter';
  figureQuality: 'web' | 'print';
  pageSize: 'a4' | 'letter' | 'custom';
  margins: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  fontFamily: string;
  fontSize: number;
  lineSpacing: number;
  chapterBreaks: boolean;    // Start each chapter on new page
  watermark?: string;
}

// Memory and context management for AI
export interface ChapterMemory {
  chapterId: string;
  title: string;
  abstract: string;          // 30-word summary for context
  keyTerms: string[];        // Important terms introduced
  mainCitations: string[];   // Most important citations (max 5)
  status: 'draft' | 'locked';
}

// Bulk operations
export interface BulkOperation {
  id: string;
  type: 'regenerate' | 'export' | 'validate';
  targetChapters: string[];
  targetSections?: string[];
  status: 'pending' | 'running' | 'completed' | 'error';
  progress: number;          // 0-100
  results?: Record<string, any>;
  error?: string;
}

// UI state management
export interface BookUIState {
  activeChapterId?: string;
  activeSectionId?: string;
  sidebarPanel: 'citations' | 'glossary' | 'memory' | 'outline';
  showOutlineTree: boolean;
  showGenerationQueue: boolean;
  editorMode: 'write' | 'review' | 'export';
  selectedCitations: string[];
  selectedTerms: string[];
}

// Validation and quality checks
export interface ValidationResult {
  chapterId: string;
  sectionId?: string;
  type: 'citation' | 'glossary' | 'structure' | 'content';
  severity: 'error' | 'warning' | 'info';
  message: string;
  suggestion?: string;
  autoFixable: boolean;
}

// Search and navigation
export interface SearchResult {
  type: 'chapter' | 'section' | 'citation' | 'term';
  id: string;
  title: string;
  excerpt: string;
  chapterId?: string;
  sectionId?: string;
  relevanceScore: number;
}
