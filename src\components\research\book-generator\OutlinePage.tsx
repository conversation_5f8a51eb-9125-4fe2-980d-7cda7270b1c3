import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, ArrowRight, List } from "lucide-react";

interface OutlinePageProps {
  onNext: () => void;
  onBack: () => void;
}

export function OutlinePage({ onNext, onBack }: OutlinePageProps) {
  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <div className="p-3 bg-purple-100 rounded-full">
            <List className="h-8 w-8 text-purple-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Outline Creation</h1>
            <p className="text-lg text-gray-600">Structure your book chapters and sections</p>
          </div>
        </div>
      </div>

      {/* Placeholder content */}
      <Card>
        <CardHeader>
          <CardTitle>Book Outline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-gray-500">
            <List className="h-16 w-16 mx-auto mb-4 text-gray-300" />
            <p className="text-lg mb-2">Outline Tree Component</p>
            <p>This will contain the hierarchical outline tree with drag-and-drop functionality</p>
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack} className="flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Setup
        </Button>
        <Button onClick={onNext} className="flex items-center gap-2">
          Continue to Generation
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
