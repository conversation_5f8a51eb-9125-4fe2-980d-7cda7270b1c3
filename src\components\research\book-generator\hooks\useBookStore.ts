import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { persist } from 'zustand/middleware';
import { nanoid } from 'nanoid';
import { 
  BookState, 
  BookMeta, 
  ChapterState, 
  OutlineNode, 
  SectionDraft, 
  Citation, 
  Term, 
  FigureReference,
  BookStage,
  BookUIState,
  GenerationQueueItem
} from '../types';
import { DEFAULT_AI_OPTIONS } from '../constants';

interface BookStore extends BookState {
  // UI state
  uiState: BookUIState;
  generationQueue: GenerationQueueItem[];
  
  // Metadata actions
  setMetadata: (metadata: Partial<BookMeta>) => void;
  resetBook: () => void;
  setStage: (stage: BookStage) => void;
  
  // Chapter actions
  addChapter: (title: string, abstract?: string) => string;
  updateChapter: (chapterId: string, updates: Partial<ChapterState>) => void;
  deleteChapter: (chapterId: string) => void;
  reorderChapters: (chapterIds: string[]) => void;
  lockChapter: (chapterId: string) => void;
  unlockChapter: (chapterId: string) => void;
  
  // Outline actions
  updateOutline: (chapterId: string, outline: OutlineNode[]) => void;
  addOutlineNode: (chapterId: string, node: Omit<OutlineNode, 'id'>) => string;
  updateOutlineNode: (chapterId: string, nodeId: string, updates: Partial<OutlineNode>) => void;
  deleteOutlineNode: (chapterId: string, nodeId: string) => void;
  reorderOutlineNodes: (chapterId: string, nodeIds: string[]) => void;
  
  // Section actions
  addSectionDraft: (chapterId: string, draft: Omit<SectionDraft, 'id' | 'lastModified'>) => string;
  updateSectionDraft: (chapterId: string, sectionId: string, updates: Partial<SectionDraft>) => void;
  deleteSectionDraft: (chapterId: string, sectionId: string) => void;
  setSectionContent: (chapterId: string, sectionId: string, content: string) => void;
  
  // Citation actions
  addCitation: (citation: Omit<Citation, 'id'>) => string;
  updateCitation: (citationId: string, updates: Partial<Citation>) => void;
  deleteCitation: (citationId: string) => void;
  mergeCitations: (citations: Citation[]) => void;
  linkCitationToChapter: (citationId: string, chapterId: string) => void;
  unlinkCitationFromChapter: (citationId: string, chapterId: string) => void;
  
  // Glossary actions
  addTerm: (term: Omit<Term, 'id'>) => string;
  updateTerm: (termId: string, updates: Partial<Term>) => void;
  deleteTerm: (termId: string) => void;
  upsertGlossaryTerm: (term: Omit<Term, 'id'>) => string;
  
  // Figure actions
  addFigure: (figure: Omit<FigureReference, 'id'>) => string;
  updateFigure: (figureId: string, updates: Partial<FigureReference>) => void;
  deleteFigure: (figureId: string) => void;
  
  // UI actions
  setActiveChapter: (chapterId?: string) => void;
  setActiveSection: (sectionId?: string) => void;
  setSidebarPanel: (panel: BookUIState['sidebarPanel']) => void;
  toggleOutlineTree: () => void;
  toggleGenerationQueue: () => void;
  setEditorMode: (mode: BookUIState['editorMode']) => void;
  
  // Generation queue actions
  addToQueue: (item: Omit<GenerationQueueItem, 'id'>) => string;
  updateQueueItem: (itemId: string, updates: Partial<GenerationQueueItem>) => void;
  removeFromQueue: (itemId: string) => void;
  clearQueue: () => void;
  
  // Utility actions
  getChapterById: (chapterId: string) => ChapterState | undefined;
  getSectionById: (chapterId: string, sectionId: string) => SectionDraft | undefined;
  getCitationById: (citationId: string) => Citation | undefined;
  getTermById: (termId: string) => Term | undefined;
  getChapterMemory: () => Array<{ chapterId: string; title: string; abstract: string; status: string }>;
  exportBookData: () => BookState;
  importBookData: (data: BookState) => void;
}

const initialBookState: BookState = {
  metadata: {
    title: '',
    subtitle: '',
    authors: [],
    audience: '',
    styleSheet: 'academic',
    synopsis: '',
    keywords: [],
    researchField: '',
    targetLength: 50000
  },
  chapters: [],
  globalGlossary: [],
  citationGraph: {},
  figureRegistry: {},
  currentStage: 'input',
  lastSaved: new Date(),
  version: '1.0.0'
};

const initialUIState: BookUIState = {
  activeChapterId: undefined,
  activeSectionId: undefined,
  sidebarPanel: 'outline',
  showOutlineTree: true,
  showGenerationQueue: false,
  editorMode: 'write',
  selectedCitations: [],
  selectedTerms: []
};

export const useBookStore = create<BookStore>()(
  persist(
    immer((set, get) => ({
      ...initialBookState,
      uiState: initialUIState,
      generationQueue: [],

      // Metadata actions
      setMetadata: (metadata) => set((state) => {
        Object.assign(state.metadata, metadata);
        state.lastSaved = new Date();
      }),

      resetBook: () => set((state) => {
        Object.assign(state, initialBookState);
        state.uiState = { ...initialUIState };
        state.generationQueue = [];
      }),

      setStage: (stage) => set((state) => {
        state.currentStage = stage;
      }),

      // Chapter actions
      addChapter: (title, abstract = '') => {
        const chapterId = nanoid();
        set((state) => {
          const newChapter: ChapterState = {
            id: chapterId,
            title,
            abstract,
            order: state.chapters.length + 1,
            outline: [],
            sections: [],
            status: 'draft',
            chapterCitations: [],
            estimatedWords: 0,
            actualWords: 0,
            lastModified: new Date()
          };
          state.chapters.push(newChapter);
          state.lastSaved = new Date();
        });
        return chapterId;
      },

      updateChapter: (chapterId, updates) => set((state) => {
        const chapter = state.chapters.find(c => c.id === chapterId);
        if (chapter) {
          Object.assign(chapter, updates);
          chapter.lastModified = new Date();
          state.lastSaved = new Date();
        }
      }),

      deleteChapter: (chapterId) => set((state) => {
        state.chapters = state.chapters.filter(c => c.id !== chapterId);
        // Clean up UI state
        if (state.uiState.activeChapterId === chapterId) {
          state.uiState.activeChapterId = undefined;
          state.uiState.activeSectionId = undefined;
        }
        state.lastSaved = new Date();
      }),

      reorderChapters: (chapterIds) => set((state) => {
        const reorderedChapters = chapterIds.map(id => 
          state.chapters.find(c => c.id === id)!
        ).filter(Boolean);
        
        reorderedChapters.forEach((chapter, index) => {
          chapter.order = index + 1;
        });
        
        state.chapters = reorderedChapters;
        state.lastSaved = new Date();
      }),

      lockChapter: (chapterId) => set((state) => {
        const chapter = state.chapters.find(c => c.id === chapterId);
        if (chapter) {
          chapter.status = 'locked';
          chapter.lastModified = new Date();
          state.lastSaved = new Date();
        }
      }),

      unlockChapter: (chapterId) => set((state) => {
        const chapter = state.chapters.find(c => c.id === chapterId);
        if (chapter) {
          chapter.status = 'draft';
          chapter.lastModified = new Date();
          state.lastSaved = new Date();
        }
      }),

      // Outline actions
      updateOutline: (chapterId, outline) => set((state) => {
        const chapter = state.chapters.find(c => c.id === chapterId);
        if (chapter) {
          chapter.outline = outline;
          chapter.lastModified = new Date();
          state.lastSaved = new Date();
        }
      }),

      addOutlineNode: (chapterId, node) => {
        const nodeId = nanoid();
        set((state) => {
          const chapter = state.chapters.find(c => c.id === chapterId);
          if (chapter) {
            const newNode: OutlineNode = {
              ...node,
              id: nodeId
            };
            chapter.outline.push(newNode);
            chapter.lastModified = new Date();
            state.lastSaved = new Date();
          }
        });
        return nodeId;
      },

      updateOutlineNode: (chapterId, nodeId, updates) => set((state) => {
        const chapter = state.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const node = chapter.outline.find(n => n.id === nodeId);
          if (node) {
            Object.assign(node, updates);
            chapter.lastModified = new Date();
            state.lastSaved = new Date();
          }
        }
      }),

      deleteOutlineNode: (chapterId, nodeId) => set((state) => {
        const chapter = state.chapters.find(c => c.id === chapterId);
        if (chapter) {
          chapter.outline = chapter.outline.filter(n => n.id !== nodeId);
          // Also remove any sections linked to this outline node
          chapter.sections = chapter.sections.filter(s => s.outlineNodeId !== nodeId);
          chapter.lastModified = new Date();
          state.lastSaved = new Date();
        }
      }),

      reorderOutlineNodes: (chapterId, nodeIds) => set((state) => {
        const chapter = state.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const reorderedNodes = nodeIds.map(id => 
            chapter.outline.find(n => n.id === id)!
          ).filter(Boolean);
          
          reorderedNodes.forEach((node, index) => {
            node.order = index + 1;
          });
          
          chapter.outline = reorderedNodes;
          chapter.lastModified = new Date();
          state.lastSaved = new Date();
        }
      }),

      // Section actions
      addSectionDraft: (chapterId, draft) => {
        const sectionId = nanoid();
        set((state) => {
          const chapter = state.chapters.find(c => c.id === chapterId);
          if (chapter) {
            const newSection: SectionDraft = {
              ...draft,
              id: sectionId,
              lastModified: new Date()
            };
            chapter.sections.push(newSection);
            chapter.actualWords = chapter.sections.reduce((sum, s) => sum + s.wordCount, 0);
            chapter.lastModified = new Date();
            state.lastSaved = new Date();
          }
        });
        return sectionId;
      },

      updateSectionDraft: (chapterId, sectionId, updates) => set((state) => {
        const chapter = state.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const section = chapter.sections.find(s => s.id === sectionId);
          if (section) {
            Object.assign(section, updates);
            section.lastModified = new Date();
            chapter.actualWords = chapter.sections.reduce((sum, s) => sum + s.wordCount, 0);
            chapter.lastModified = new Date();
            state.lastSaved = new Date();
          }
        }
      }),

      deleteSectionDraft: (chapterId, sectionId) => set((state) => {
        const chapter = state.chapters.find(c => c.id === chapterId);
        if (chapter) {
          chapter.sections = chapter.sections.filter(s => s.id !== sectionId);
          chapter.actualWords = chapter.sections.reduce((sum, s) => sum + s.wordCount, 0);
          chapter.lastModified = new Date();
          state.lastSaved = new Date();
        }
      }),

      setSectionContent: (chapterId, sectionId, content) => set((state) => {
        const chapter = state.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const section = chapter.sections.find(s => s.id === sectionId);
          if (section) {
            section.content = content;
            section.wordCount = content.split(/\s+/).filter(word => word.length > 0).length;
            section.lastModified = new Date();
            chapter.actualWords = chapter.sections.reduce((sum, s) => sum + s.wordCount, 0);
            chapter.lastModified = new Date();
            state.lastSaved = new Date();
          }
        }
      }),

      // Citation actions
      addCitation: (citation) => {
        const citationId = nanoid();
        set((state) => {
          const newCitation: Citation = {
            ...citation,
            id: citationId
          };
          state.citationGraph[citationId] = newCitation;
          state.lastSaved = new Date();
        });
        return citationId;
      },

      updateCitation: (citationId, updates) => set((state) => {
        const citation = state.citationGraph[citationId];
        if (citation) {
          Object.assign(citation, updates);
          state.lastSaved = new Date();
        }
      }),

      deleteCitation: (citationId) => set((state) => {
        delete state.citationGraph[citationId];
        // Remove from all chapters
        state.chapters.forEach(chapter => {
          chapter.chapterCitations = chapter.chapterCitations.filter(id => id !== citationId);
          chapter.sections.forEach(section => {
            section.citations = section.citations.filter(id => id !== citationId);
          });
        });
        state.lastSaved = new Date();
      }),

      mergeCitations: (citations) => set((state) => {
        citations.forEach(citation => {
          if (!state.citationGraph[citation.id]) {
            state.citationGraph[citation.id] = citation;
          }
        });
        state.lastSaved = new Date();
      }),

      linkCitationToChapter: (citationId, chapterId) => set((state) => {
        const chapter = state.chapters.find(c => c.id === chapterId);
        const citation = state.citationGraph[citationId];
        if (chapter && citation) {
          if (!chapter.chapterCitations.includes(citationId)) {
            chapter.chapterCitations.push(citationId);
          }
          if (!citation.chapterIds.includes(chapterId)) {
            citation.chapterIds.push(chapterId);
          }
          state.lastSaved = new Date();
        }
      }),

      unlinkCitationFromChapter: (citationId, chapterId) => set((state) => {
        const chapter = state.chapters.find(c => c.id === chapterId);
        const citation = state.citationGraph[citationId];
        if (chapter && citation) {
          chapter.chapterCitations = chapter.chapterCitations.filter(id => id !== citationId);
          citation.chapterIds = citation.chapterIds.filter(id => id !== chapterId);
          state.lastSaved = new Date();
        }
      }),

      // Glossary actions
      addTerm: (term) => {
        const termId = nanoid();
        set((state) => {
          const newTerm: Term = {
            ...term,
            id: termId
          };
          state.globalGlossary.push(newTerm);
          state.lastSaved = new Date();
        });
        return termId;
      },

      updateTerm: (termId, updates) => set((state) => {
        const term = state.globalGlossary.find(t => t.id === termId);
        if (term) {
          Object.assign(term, updates);
          state.lastSaved = new Date();
        }
      }),

      deleteTerm: (termId) => set((state) => {
        state.globalGlossary = state.globalGlossary.filter(t => t.id !== termId);
        state.lastSaved = new Date();
      }),

      upsertGlossaryTerm: (term) => {
        const existingTerm = get().globalGlossary.find(t => 
          t.term.toLowerCase() === term.term.toLowerCase()
        );
        
        if (existingTerm) {
          get().updateTerm(existingTerm.id, term);
          return existingTerm.id;
        } else {
          return get().addTerm(term);
        }
      },

      // Figure actions
      addFigure: (figure) => {
        const figureId = nanoid();
        set((state) => {
          const newFigure: FigureReference = {
            ...figure,
            id: figureId
          };
          state.figureRegistry[figureId] = newFigure;
          state.lastSaved = new Date();
        });
        return figureId;
      },

      updateFigure: (figureId, updates) => set((state) => {
        const figure = state.figureRegistry[figureId];
        if (figure) {
          Object.assign(figure, updates);
          state.lastSaved = new Date();
        }
      }),

      deleteFigure: (figureId) => set((state) => {
        delete state.figureRegistry[figureId];
        state.lastSaved = new Date();
      }),

      // UI actions
      setActiveChapter: (chapterId) => set((state) => {
        state.uiState.activeChapterId = chapterId;
        state.uiState.activeSectionId = undefined; // Reset section when changing chapter
      }),

      setActiveSection: (sectionId) => set((state) => {
        state.uiState.activeSectionId = sectionId;
      }),

      setSidebarPanel: (panel) => set((state) => {
        state.uiState.sidebarPanel = panel;
      }),

      toggleOutlineTree: () => set((state) => {
        state.uiState.showOutlineTree = !state.uiState.showOutlineTree;
      }),

      toggleGenerationQueue: () => set((state) => {
        state.uiState.showGenerationQueue = !state.uiState.showGenerationQueue;
      }),

      setEditorMode: (mode) => set((state) => {
        state.uiState.editorMode = mode;
      }),

      // Generation queue actions
      addToQueue: (item) => {
        const itemId = nanoid();
        set((state) => {
          const newItem: GenerationQueueItem = {
            ...item,
            id: itemId
          };
          state.generationQueue.push(newItem);
        });
        return itemId;
      },

      updateQueueItem: (itemId, updates) => set((state) => {
        const item = state.generationQueue.find(i => i.id === itemId);
        if (item) {
          Object.assign(item, updates);
        }
      }),

      removeFromQueue: (itemId) => set((state) => {
        state.generationQueue = state.generationQueue.filter(i => i.id !== itemId);
      }),

      clearQueue: () => set((state) => {
        state.generationQueue = [];
      }),

      // Utility actions
      getChapterById: (chapterId) => {
        return get().chapters.find(c => c.id === chapterId);
      },

      getSectionById: (chapterId, sectionId) => {
        const chapter = get().chapters.find(c => c.id === chapterId);
        return chapter?.sections.find(s => s.id === sectionId);
      },

      getCitationById: (citationId) => {
        return get().citationGraph[citationId];
      },

      getTermById: (termId) => {
        return get().globalGlossary.find(t => t.id === termId);
      },

      getChapterMemory: () => {
        return get().chapters
          .filter(c => c.status === 'locked')
          .slice(-5) // Last 5 locked chapters
          .map(c => ({
            chapterId: c.id,
            title: c.title,
            abstract: c.abstract.slice(0, 150) + (c.abstract.length > 150 ? '...' : ''),
            status: c.status
          }));
      },

      exportBookData: () => {
        const state = get();
        return {
          metadata: state.metadata,
          chapters: state.chapters,
          globalGlossary: state.globalGlossary,
          citationGraph: state.citationGraph,
          figureRegistry: state.figureRegistry,
          currentStage: state.currentStage,
          lastSaved: state.lastSaved,
          version: state.version
        };
      },

      importBookData: (data) => set((state) => {
        Object.assign(state, data);
        state.lastSaved = new Date();
      })
    })),
    {
      name: 'pgp-book',
      partialize: (state) => ({
        metadata: state.metadata,
        chapters: state.chapters,
        globalGlossary: state.globalGlossary,
        citationGraph: state.citationGraph,
        figureRegistry: state.figureRegistry,
        currentStage: state.currentStage,
        lastSaved: state.lastSaved,
        version: state.version
      })
    }
  )
);
