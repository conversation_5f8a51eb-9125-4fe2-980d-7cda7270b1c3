import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { 
  ChevronDown, 
  ChevronRight, 
  Plus, 
  Edit2, 
  Trash2, 
  GripVertical,
  Check,
  X,
  FileText,
  List
} from "lucide-react";
import { useOutlineTree } from '../hooks/useOutlineTree';
import { OutlineNode } from '../types';

interface OutlineTreeProps {
  chapterId: string;
  className?: string;
}

interface TreeNodeProps {
  node: any; // OutlineTreeNode from useOutlineTree
  onEdit: (nodeId: string, title: string) => void;
  onDelete: (nodeId: string) => void;
  onAddChild: (parentId: string) => void;
  onAddSibling: (afterId: string) => void;
  onToggleExpanded: (nodeId: string) => void;
  onSelect: (nodeId: string) => void;
  level: number;
}

function TreeNode({ 
  node, 
  onEdit, 
  onDelete, 
  onAddChild, 
  onAddSibling, 
  onToggleExpanded, 
  onSelect,
  level 
}: TreeNodeProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editTitle, setEditTitle] = useState(node.title);

  const handleSaveEdit = () => {
    if (editTitle.trim()) {
      onEdit(node.id, editTitle.trim());
      setIsEditing(false);
    }
  };

  const handleCancelEdit = () => {
    setEditTitle(node.title);
    setIsEditing(false);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  const hasChildren = node.children && node.children.length > 0;
  const canHaveChildren = node.level === 1; // Only sections can have subsections

  return (
    <div className="outline-tree-node">
      <div 
        className={`
          flex items-center gap-2 p-2 rounded-lg hover:bg-gray-50 transition-colors
          ${node.isSelected ? 'bg-blue-50 border border-blue-200' : ''}
          ${level > 0 ? 'ml-6' : ''}
        `}
        onClick={() => onSelect(node.id)}
      >
        {/* Expand/Collapse button */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            onToggleExpanded(node.id);
          }}
          className="p-1 hover:bg-gray-200 rounded"
          disabled={!hasChildren && !canHaveChildren}
        >
          {hasChildren ? (
            node.isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )
          ) : (
            <div className="w-4 h-4" />
          )}
        </button>

        {/* Drag handle */}
        <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />

        {/* Icon */}
        {node.level === 1 ? (
          <FileText className="h-4 w-4 text-blue-600" />
        ) : (
          <List className="h-4 w-4 text-purple-600" />
        )}

        {/* Title */}
        <div className="flex-1">
          {isEditing ? (
            <Input
              value={editTitle}
              onChange={(e) => setEditTitle(e.target.value)}
              onKeyDown={handleKeyPress}
              onBlur={handleSaveEdit}
              className="h-8 text-sm"
              autoFocus
            />
          ) : (
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm">{node.title}</span>
              <Badge variant="outline" className="text-xs">
                {node.status}
              </Badge>
              {node.estimatedWords && (
                <Badge variant="secondary" className="text-xs">
                  ~{node.estimatedWords} words
                </Badge>
              )}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          {isEditing ? (
            <>
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  handleSaveEdit();
                }}
                className="h-6 w-6 p-0"
              >
                <Check className="h-3 w-3" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  handleCancelEdit();
                }}
                className="h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </>
          ) : (
            <>
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsEditing(true);
                }}
                className="h-6 w-6 p-0"
              >
                <Edit2 className="h-3 w-3" />
              </Button>
              
              {canHaveChildren && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={(e) => {
                    e.stopPropagation();
                    onAddChild(node.id);
                  }}
                  className="h-6 w-6 p-0"
                  title="Add subsection"
                >
                  <Plus className="h-3 w-3" />
                </Button>
              )}
              
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  onAddSibling(node.id);
                }}
                className="h-6 w-6 p-0"
                title="Add sibling"
              >
                <Plus className="h-3 w-3" />
              </Button>
              
              <Button
                size="sm"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(node.id);
                }}
                className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Children */}
      {hasChildren && node.isExpanded && (
        <div className="ml-4">
          {node.children.map((child: any) => (
            <TreeNode
              key={child.id}
              node={child}
              onEdit={onEdit}
              onDelete={onDelete}
              onAddChild={onAddChild}
              onAddSibling={onAddSibling}
              onToggleExpanded={onToggleExpanded}
              onSelect={onSelect}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  );
}

export function OutlineTree({ chapterId, className }: OutlineTreeProps) {
  const {
    treeData,
    selectedNodeId,
    addNode,
    updateNode,
    deleteNode,
    toggleExpanded,
    selectNode,
    expandAll,
    collapseAll,
    getTotalWords,
    getCompletionStats
  } = useOutlineTree({ chapterId });

  const stats = getCompletionStats();
  const totalWords = getTotalWords();

  const handleEdit = (nodeId: string, title: string) => {
    updateNode(nodeId, { title });
  };

  const handleDelete = (nodeId: string) => {
    if (confirm('Are you sure you want to delete this section and all its subsections?')) {
      deleteNode(nodeId);
    }
  };

  const handleAddChild = (parentId: string) => {
    addNode(parentId);
  };

  const handleAddSibling = (afterId: string) => {
    const afterNode = treeData.find(n => n.id === afterId);
    addNode(afterNode?.parentId, afterId);
  };

  const handleAddRootSection = () => {
    addNode();
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <List className="h-5 w-5" />
            Chapter Outline
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={expandAll}
              className="text-xs"
            >
              Expand All
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={collapseAll}
              className="text-xs"
            >
              Collapse All
            </Button>
          </div>
        </div>
        
        {/* Stats */}
        <div className="flex items-center gap-4 text-sm text-gray-600">
          <span>{stats.total} sections</span>
          <span>~{totalWords} words</span>
          <span>{Math.round(stats.completionRate * 100)}% complete</span>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-2">
          {treeData.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <List className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg mb-2">No sections yet</p>
              <p className="mb-4">Start by adding your first section</p>
              <Button onClick={handleAddRootSection}>
                <Plus className="h-4 w-4 mr-2" />
                Add Section
              </Button>
            </div>
          ) : (
            <>
              <div className="group">
                {treeData.map((node) => (
                  <TreeNode
                    key={node.id}
                    node={node}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                    onAddChild={handleAddChild}
                    onAddSibling={handleAddSibling}
                    onToggleExpanded={toggleExpanded}
                    onSelect={selectNode}
                    level={0}
                  />
                ))}
              </div>
              
              <div className="pt-4 border-t">
                <Button
                  variant="outline"
                  onClick={handleAddRootSection}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Section
                </Button>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
