import { use<PERSON><PERSON><PERSON>, EditorContent, Editor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextStyle from '@tiptap/extension-text-style';
import FontFamily from '@tiptap/extension-font-family';
import Highlight from '@tiptap/extension-highlight';
import Color from '@tiptap/extension-color';
import BulletList from '@tiptap/extension-bullet-list';
import OrderedList from '@tiptap/extension-ordered-list';
import ListItem from '@tiptap/extension-list-item';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import Typography from '@tiptap/extension-typography';
import Placeholder from '@tiptap/extension-placeholder';
import { ResizableImage } from 'tiptap-extension-resizable-image';
import { useCallback, forwardRef, useImperativeHandle } from 'react';
import ImageAlignment from './extensions/image-alignment';
import { GripVertical } from 'lucide-react';
import './editor-styles.css';

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  onSelectionChange?: () => void;
  className?: string;
  editable?: boolean;
}

export interface RichTextEditorRef {
  editor: Editor | null;
  insertContent: (content: string) => void;
  getSelectedText: () => string;
  getSelectionRange: () => { from: number; to: number } | null;
  replaceSelectedText: (text: string) => void;
  applyFormat: (format: string, value?: any) => void;
  getHTML: () => string;
  getText: () => string;
  focus: () => void;
}

const RichTextEditor = forwardRef<RichTextEditorRef, RichTextEditorProps>(
  ({ content, onChange, onSelectionChange, className = '', editable = true }, ref) => {
    const editor = useEditor({
      extensions: [
        StarterKit.configure({
          heading: {
            levels: [1, 2, 3, 4]
          },
          // Disable the built-in bullet/ordered list to use our custom ones
          bulletList: false,
          orderedList: false,
        }),
        BulletList.configure({
          HTMLAttributes: {
            class: 'rich-editor-bullet-list',
          },
        }),
        OrderedList.configure({
          HTMLAttributes: {
            class: 'rich-editor-ordered-list',
          },
        }),
        ListItem.configure({
          HTMLAttributes: {
            class: 'rich-editor-list-item',
          },
        }),
        Underline,
        TextStyle,
        FontFamily,
        Highlight,
        Color,
        ResizableImage.configure({
          allowBase64: true,
        }),
        ImageAlignment.configure({
          types: ['resizableImage'],
        }),
        Table.configure({
          resizable: true,
          handleWidth: 5,
          cellMinWidth: 25,
        }),
        TableRow,
        TableCell.configure({
          HTMLAttributes: {
            style: 'border: 1px solid #e5e7eb; padding: 8px; vertical-align: top;'
          }
        }),
        TableHeader.configure({
          HTMLAttributes: {
            style: 'border: 1px solid #e5e7eb; padding: 8px; background-color: #f9fafb; font-weight: bold;'
          }
        }),
        Link.configure({
          openOnClick: false,
        }),
        TextAlign.configure({
          types: ['heading', 'paragraph'],
        }),
        Typography,
        Placeholder.configure({
          placeholder: ({ node }) => {
            if (node.type.name === 'heading') {
              return 'What\'s the title?'
            }
            return 'Start writing your research paper here... Use "/" for commands or select text for AI assistance.'
          },
        }),
      ],
      content: convertMarkdownToHTML(content),
      editable,
      editorProps: {
        attributes: {
          class: 'modern-editor-content',
        },
      },
      onUpdate: ({ editor }) => {
        const html = editor.getHTML();
        onChange(html);
      },
      onSelectionUpdate: ({ editor }) => {
        // Call the selection change handler if provided
        if (onSelectionChange) {
          onSelectionChange();
        }
      },
    });

    // Convert basic markdown to HTML for initial rendering
    function convertMarkdownToHTML(markdown: string): string {
      // Basic conversion for initial content - will be replaced by proper HTML editing
      let html = markdown
        // Headers
        .replace(/^# (.+)$/gm, '<h1>$1</h1>')
        .replace(/^## (.+)$/gm, '<h2>$1</h2>')
        .replace(/^### (.+)$/gm, '<h3>$1</h3>')
        // Bold & Italic
        .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.+?)\*/g, '<em>$1</em>')
        // Lists
        .replace(/^\- (.+)$/gm, '<ul><li>$1</li></ul>')
        .replace(/^\d+\. (.+)$/gm, '<ol><li>$1</li></ol>')
        // Fix nested lists
        .replace(/<\/ul>\n<ul>/g, '')
        .replace(/<\/ol>\n<ol>/g, '')
        // Paragraphs
        .replace(/^(?!<[oh][lu]>|<li>|<h[1-6]>)(.+)$/gm, '<p>$1</p>')
        // Fix empty lines
        .replace(/<p><\/p>/g, '<p><br></p>');

      return html;
    }

    // Expose methods to the parent component
    useImperativeHandle(ref, () => ({
      editor,
      
      insertContent: (content: string) => {
        editor?.commands.insertContent(content);
      },
      
      getSelectedText: () => {
        return editor?.state.doc.textBetween(
          editor.state.selection.from,
          editor.state.selection.to,
          ' '
        ) || '';
      },
      
      getSelectionRange: () => {
        if (!editor?.state.selection) return null;
        return {
          from: editor.state.selection.from,
          to: editor.state.selection.to
        };
      },
      
      replaceSelectedText: (text: string) => {
        if (!editor) return;
        const { from, to } = editor.state.selection;
        editor.commands.deleteRange({ from, to });
        editor.commands.insertContent(text);
      },
      
      applyFormat: (format: string, value: any = null) => {
        if (!editor) return;
        
        switch (format) {
          case 'bold':
            editor.commands.toggleBold();
            break;
          case 'italic':
            editor.commands.toggleItalic();
            break;
          case 'underline':
            editor.commands.toggleUnderline();
            break;
          case 'h1':
            editor.commands.toggleHeading({ level: 1 });
            break;
          case 'h2':
            editor.commands.toggleHeading({ level: 2 });
            break;
          case 'h3':
            editor.commands.toggleHeading({ level: 3 });
            break;
          case 'ul':
          case 'list':
            editor.commands.toggleBulletList();
            break;
          case 'ol':
          case 'ordered-list':
            editor.commands.toggleOrderedList();
            break;
          case 'quote':
            editor.commands.toggleBlockquote();
            break;
          case 'code':
            editor.commands.toggleCodeBlock();
            break;
          case 'highlight':
            editor.commands.toggleHighlight();
            break;
          case 'font-family':
            editor.commands.setFontFamily(value);
            break;
          case 'color':
            editor.commands.setColor(value);
            break;
          case 'image':
            if (value) {
              editor.chain().focus().setResizableImage({ src: value, width: 400 }).run();
            }
            break;
          case 'image-align-left':
            editor.chain().focus().setImageAlignment('left').run();
            break;
          case 'image-align-center':
            editor.chain().focus().setImageAlignment('center').run();
            break;
          case 'image-align-right':
            editor.chain().focus().setImageAlignment('right').run();
            break;
          case 'table':
            if (value && value.rows && value.cols) {
              editor.chain().focus().insertTable({ rows: value.rows, cols: value.cols, withHeaderRow: true }).run();
            } else {
              editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
            }
            break;
          case 'align-left':
            editor.chain().focus().setTextAlign('left').run();
            break;
          case 'align-center':
            editor.chain().focus().setTextAlign('center').run();
            break;
          case 'align-right':
            editor.chain().focus().setTextAlign('right').run();
            break;
          case 'align-justify':
            editor.chain().focus().setTextAlign('justify').run();
            break;
        }
      },
      
      getHTML: () => {
        return editor?.getHTML() || '';
      },
      
      getText: () => {
        return editor?.getText() || '';
      },
      
      focus: () => {
        editor?.commands.focus();
      }
    }));

    // Custom render for images to add drag handle
    const renderImageWithHandle = (node: any, isSelected: boolean) => {
      return (
        <span className={`resizable-image-wrapper${isSelected ? ' selected' : ''}`}
          contentEditable={false}
          draggable={true}
          onDragStart={e => {
            // Let Tiptap handle drag
          }}
        >
          <img src={node.attrs.src} style={{ width: node.attrs.width || 'auto', height: node.attrs.height || 'auto', borderRadius: 8 }} alt="figure" />
          {isSelected && (
            <span className="image-drag-handle" title="Drag to move image">
              <GripVertical />
            </span>
          )}
        </span>
      );
    };

    return (
      <EditorContent 
        editor={editor} 
        className={className} 
        // Add custom nodeViews for images
        nodeViews={{
          resizableImage: ({ node, getPos, decorations, selected }) => {
            return renderImageWithHandle(node, selected);
          }
        }}
      />
    );
  }
);

RichTextEditor.displayName = 'RichTextEditor';

export default RichTextEditor;
