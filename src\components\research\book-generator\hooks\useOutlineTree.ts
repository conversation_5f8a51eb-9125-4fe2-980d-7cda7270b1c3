import { useState, useCallback, useMemo } from 'react';
import { nanoid } from 'nanoid';
import { OutlineNode } from '../types';
import { useBookStore } from './useBookStore';

export interface OutlineTreeNode extends OutlineNode {
  children: OutlineTreeNode[];
  isExpanded: boolean;
  isSelected: boolean;
}

interface UseOutlineTreeProps {
  chapterId: string;
}

export function useOutlineTree({ chapterId }: UseOutlineTreeProps) {
  const { 
    getChapterById, 
    updateOutline, 
    addOutlineNode, 
    updateOutlineNode, 
    deleteOutlineNode,
    reorderOutlineNodes 
  } = useBookStore();
  
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [draggedNodeId, setDraggedNodeId] = useState<string | null>(null);

  const chapter = getChapterById(chapterId);
  const outline = chapter?.outline || [];

  // Convert flat outline to tree structure
  const treeData = useMemo(() => {
    const nodeMap = new Map<string, OutlineTreeNode>();
    const rootNodes: OutlineTreeNode[] = [];

    // First pass: create all nodes
    outline.forEach(node => {
      const treeNode: OutlineTreeNode = {
        ...node,
        children: [],
        isExpanded: expandedNodes.has(node.id),
        isSelected: selectedNodeId === node.id
      };
      nodeMap.set(node.id, treeNode);
    });

    // Second pass: build tree structure
    outline.forEach(node => {
      const treeNode = nodeMap.get(node.id)!;
      if (node.parentId && nodeMap.has(node.parentId)) {
        const parent = nodeMap.get(node.parentId)!;
        parent.children.push(treeNode);
      } else {
        rootNodes.push(treeNode);
      }
    });

    // Sort by order
    const sortByOrder = (nodes: OutlineTreeNode[]) => {
      nodes.sort((a, b) => a.order - b.order);
      nodes.forEach(node => sortByOrder(node.children));
    };
    sortByOrder(rootNodes);

    return rootNodes;
  }, [outline, expandedNodes, selectedNodeId]);

  // Node manipulation functions
  const addNode = useCallback((parentId?: string, insertAfter?: string) => {
    const parentNode = parentId ? outline.find(n => n.id === parentId) : null;
    const level = parentNode ? parentNode.level + 1 : 1;
    
    // Calculate order
    let order = 1;
    if (insertAfter) {
      const afterNode = outline.find(n => n.id === insertAfter);
      if (afterNode) {
        order = afterNode.order + 1;
        // Increment order of subsequent nodes
        outline
          .filter(n => n.order > afterNode.order && n.parentId === parentId)
          .forEach(n => n.order++);
      }
    } else if (parentId) {
      const siblings = outline.filter(n => n.parentId === parentId);
      order = siblings.length > 0 ? Math.max(...siblings.map(n => n.order)) + 1 : 1;
    } else {
      const rootNodes = outline.filter(n => !n.parentId);
      order = rootNodes.length > 0 ? Math.max(...rootNodes.map(n => n.order)) + 1 : 1;
    }

    const newNode: Omit<OutlineNode, 'id'> = {
      title: level === 1 ? 'New Section' : 'New Subsection',
      description: '',
      level,
      order,
      parentId,
      estimatedWords: level === 1 ? 1000 : 500,
      status: 'draft'
    };

    const nodeId = addOutlineNode(chapterId, newNode);
    setSelectedNodeId(nodeId);
    
    // Auto-expand parent if adding child
    if (parentId) {
      setExpandedNodes(prev => new Set([...prev, parentId]));
    }

    return nodeId;
  }, [chapterId, outline, addOutlineNode]);

  const updateNode = useCallback((nodeId: string, updates: Partial<OutlineNode>) => {
    updateOutlineNode(chapterId, nodeId, updates);
  }, [chapterId, updateOutlineNode]);

  const deleteNode = useCallback((nodeId: string) => {
    // Also delete all children
    const nodesToDelete = [nodeId];
    const findChildren = (parentId: string) => {
      outline
        .filter(n => n.parentId === parentId)
        .forEach(child => {
          nodesToDelete.push(child.id);
          findChildren(child.id);
        });
    };
    findChildren(nodeId);

    nodesToDelete.forEach(id => {
      deleteOutlineNode(chapterId, id);
    });

    // Clear selection if deleted node was selected
    if (selectedNodeId === nodeId) {
      setSelectedNodeId(null);
    }
  }, [chapterId, outline, selectedNodeId, deleteOutlineNode]);

  const moveNode = useCallback((nodeId: string, newParentId?: string, newOrder?: number) => {
    const node = outline.find(n => n.id === nodeId);
    if (!node) return;

    const updates: Partial<OutlineNode> = {};
    
    if (newParentId !== undefined) {
      updates.parentId = newParentId || undefined;
      // Update level based on new parent
      const newParent = newParentId ? outline.find(n => n.id === newParentId) : null;
      updates.level = newParent ? newParent.level + 1 : 1;
    }

    if (newOrder !== undefined) {
      updates.order = newOrder;
    }

    updateNode(nodeId, updates);
  }, [outline, updateNode]);

  const reorderNodes = useCallback((nodeIds: string[]) => {
    reorderOutlineNodes(chapterId, nodeIds);
  }, [chapterId, reorderOutlineNodes]);

  // Tree interaction functions
  const toggleExpanded = useCallback((nodeId: string) => {
    setExpandedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
      } else {
        newSet.add(nodeId);
      }
      return newSet;
    });
  }, []);

  const selectNode = useCallback((nodeId: string | null) => {
    setSelectedNodeId(nodeId);
  }, []);

  const expandAll = useCallback(() => {
    const allNodeIds = outline.map(n => n.id);
    setExpandedNodes(new Set(allNodeIds));
  }, [outline]);

  const collapseAll = useCallback(() => {
    setExpandedNodes(new Set());
  }, []);

  // Drag and drop functions
  const startDrag = useCallback((nodeId: string) => {
    setDraggedNodeId(nodeId);
  }, []);

  const endDrag = useCallback(() => {
    setDraggedNodeId(null);
  }, []);

  const canDrop = useCallback((draggedId: string, targetId: string, position: 'before' | 'after' | 'inside') => {
    if (draggedId === targetId) return false;
    
    const draggedNode = outline.find(n => n.id === draggedId);
    const targetNode = outline.find(n => n.id === targetId);
    
    if (!draggedNode || !targetNode) return false;

    // Prevent dropping a parent into its own child
    const isDescendant = (ancestorId: string, descendantId: string): boolean => {
      const descendant = outline.find(n => n.id === descendantId);
      if (!descendant || !descendant.parentId) return false;
      if (descendant.parentId === ancestorId) return true;
      return isDescendant(ancestorId, descendant.parentId);
    };

    if (isDescendant(draggedId, targetId)) return false;

    // Check level constraints
    if (position === 'inside') {
      // Can only drop into level 1 nodes (sections), not subsections
      return targetNode.level === 1 && draggedNode.level <= 2;
    }

    return true;
  }, [outline]);

  const handleDrop = useCallback((draggedId: string, targetId: string, position: 'before' | 'after' | 'inside') => {
    if (!canDrop(draggedId, targetId, position)) return false;

    const targetNode = outline.find(n => n.id === targetId);
    if (!targetNode) return false;

    if (position === 'inside') {
      moveNode(draggedId, targetId, 1);
    } else {
      const newOrder = position === 'before' ? targetNode.order : targetNode.order + 1;
      moveNode(draggedId, targetNode.parentId, newOrder);
    }

    return true;
  }, [outline, canDrop, moveNode]);

  // Utility functions
  const getNodePath = useCallback((nodeId: string): OutlineNode[] => {
    const path: OutlineNode[] = [];
    let currentId: string | undefined = nodeId;
    
    while (currentId) {
      const node = outline.find(n => n.id === currentId);
      if (!node) break;
      path.unshift(node);
      currentId = node.parentId;
    }
    
    return path;
  }, [outline]);

  const getNodeDepth = useCallback((nodeId: string): number => {
    return getNodePath(nodeId).length;
  }, [getNodePath]);

  const getTotalWords = useCallback((): number => {
    return outline.reduce((sum, node) => sum + (node.estimatedWords || 0), 0);
  }, [outline]);

  const getCompletionStats = useCallback() => {
    const total = outline.length;
    const approved = outline.filter(n => n.status === 'approved').length;
    const locked = outline.filter(n => n.status === 'locked').length;
    
    return {
      total,
      approved,
      locked,
      draft: total - approved - locked,
      completionRate: total > 0 ? (approved + locked) / total : 0
    };
  }, [outline]);

  return {
    // Data
    treeData,
    outline,
    selectedNodeId,
    draggedNodeId,
    expandedNodes,
    
    // Node operations
    addNode,
    updateNode,
    deleteNode,
    moveNode,
    reorderNodes,
    
    // Tree interactions
    toggleExpanded,
    selectNode,
    expandAll,
    collapseAll,
    
    // Drag and drop
    startDrag,
    endDrag,
    canDrop,
    handleDrop,
    
    // Utilities
    getNodePath,
    getNodeDepth,
    getTotalWords,
    getCompletionStats
  };
}
