import React, { useEffect } from 'react';
import { toast } from "sonner";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  BookOpen, 
  FileText, 
  List, 
  Zap, 
  Edit3, 
  Download,
  ArrowLeft,
  ArrowRight,
  CheckCircle
} from "lucide-react";

import { useBookStore } from './hooks/useBookStore';
import { BOOK_STAGES } from './constants';
import { BookStage } from './types';

// Import stage components (will be created next)
import { BookMetadataForm } from './BookMetadataForm';
import { OutlinePage } from './OutlinePage';
import { GenerationPage } from './GenerationPage';
import { EditorPage } from './EditorPage';
import { ExportWizard } from './ExportWizard';

const STAGE_ICONS = {
  input: FileText,
  outline: List,
  chapterQueue: Zap,
  editor: Edit3,
  export: Download
};

const STAGE_COLORS = {
  input: 'blue',
  outline: 'purple',
  chapterQueue: 'orange',
  editor: 'green',
  export: 'indigo'
};

export function BookGenerator() {
  const { 
    currentStage, 
    setStage, 
    metadata, 
    chapters,
    resetBook,
    lastSaved
  } = useBookStore();

  // Auto-save indicator
  useEffect(() => {
    const interval = setInterval(() => {
      // Auto-save is handled by the store persistence
      // This could trigger a visual indicator
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Stage navigation functions
  const canProceedToStage = (stage: BookStage): boolean => {
    switch (stage) {
      case 'input':
        return true;
      case 'outline':
        return !!(metadata.title && metadata.authors.length > 0 && metadata.synopsis);
      case 'chapterQueue':
        return chapters.length > 0 && chapters.some(c => c.outline.length > 0);
      case 'editor':
        return chapters.some(c => c.sections.length > 0);
      case 'export':
        return chapters.some(c => c.status === 'locked' || c.sections.some(s => s.status === 'completed'));
      default:
        return false;
    }
  };

  const goToStage = (stage: BookStage) => {
    if (canProceedToStage(stage)) {
      setStage(stage);
    } else {
      toast.error(`Cannot proceed to ${stage}. Please complete the previous steps.`);
    }
  };

  const nextStage = () => {
    const currentIndex = BOOK_STAGES.findIndex(s => s.id === currentStage);
    if (currentIndex < BOOK_STAGES.length - 1) {
      const nextStageId = BOOK_STAGES[currentIndex + 1].id as BookStage;
      goToStage(nextStageId);
    }
  };

  const previousStage = () => {
    const currentIndex = BOOK_STAGES.findIndex(s => s.id === currentStage);
    if (currentIndex > 0) {
      const prevStageId = BOOK_STAGES[currentIndex - 1].id as BookStage;
      setStage(prevStageId);
    }
  };

  // Calculate overall progress
  const calculateProgress = (): number => {
    let progress = 0;
    
    // Input stage (20%)
    if (metadata.title && metadata.authors.length > 0 && metadata.synopsis) {
      progress += 20;
    }
    
    // Outline stage (20%)
    if (chapters.length > 0 && chapters.some(c => c.outline.length > 0)) {
      progress += 20;
    }
    
    // Generation stage (30%)
    const totalSections = chapters.reduce((sum, c) => sum + c.outline.length, 0);
    const completedSections = chapters.reduce((sum, c) => 
      sum + c.sections.filter(s => s.status === 'completed').length, 0
    );
    if (totalSections > 0) {
      progress += Math.round((completedSections / totalSections) * 30);
    }
    
    // Editor stage (20%)
    const lockedChapters = chapters.filter(c => c.status === 'locked').length;
    if (chapters.length > 0) {
      progress += Math.round((lockedChapters / chapters.length) * 20);
    }
    
    // Export stage (10%)
    if (chapters.some(c => c.status === 'locked')) {
      progress += 10;
    }
    
    return Math.min(progress, 100);
  };

  const handleReset = () => {
    if (confirm('Are you sure you want to reset the entire book? This action cannot be undone.')) {
      resetBook();
      toast.success('Book has been reset');
    }
  };

  const renderStageContent = () => {
    switch (currentStage) {
      case 'input':
        return <BookMetadataForm onNext={() => goToStage('outline')} />;
      case 'outline':
        return <OutlinePage onNext={() => goToStage('chapterQueue')} onBack={() => goToStage('input')} />;
      case 'chapterQueue':
        return <GenerationPage onNext={() => goToStage('editor')} onBack={() => goToStage('outline')} />;
      case 'editor':
        return <EditorPage onNext={() => goToStage('export')} onBack={() => goToStage('chapterQueue')} />;
      case 'export':
        return <ExportWizard onBack={() => goToStage('editor')} />;
      default:
        return <div>Unknown stage</div>;
    }
  };

  const currentStageInfo = BOOK_STAGES.find(s => s.id === currentStage);
  const StageIcon = STAGE_ICONS[currentStage];
  const stageColor = STAGE_COLORS[currentStage];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header with progress and navigation */}
      <div className="sticky top-0 z-50 bg-white/95 backdrop-blur-sm border-b shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Title and current stage */}
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <BookOpen className="h-8 w-8 text-blue-600" />
                <div>
                  <h1 className="text-xl font-bold text-gray-900">
                    {metadata.title || 'AI Book Generator'}
                  </h1>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <StageIcon className="h-4 w-4" />
                    <span>{currentStageInfo?.name}</span>
                    <Badge variant="outline" className={`text-${stageColor}-600 border-${stageColor}-200`}>
                      Step {BOOK_STAGES.findIndex(s => s.id === currentStage) + 1} of {BOOK_STAGES.length}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            {/* Progress and actions */}
            <div className="flex items-center gap-4">
              <div className="hidden md:flex items-center gap-2 text-sm text-gray-600">
                <span>Progress:</span>
                <div className="w-32">
                  <Progress value={calculateProgress()} className="h-2" />
                </div>
                <span className="font-medium">{calculateProgress()}%</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={previousStage}
                  disabled={currentStage === 'input'}
                  className="hidden sm:flex"
                >
                  <ArrowLeft className="h-4 w-4 mr-1" />
                  Back
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReset}
                  className="text-red-600 hover:text-red-700"
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>

          {/* Stage navigation breadcrumb */}
          <div className="mt-4 flex items-center gap-2 overflow-x-auto pb-2">
            {BOOK_STAGES.map((stage, index) => {
              const Icon = STAGE_ICONS[stage.id as BookStage];
              const isActive = stage.id === currentStage;
              const isCompleted = canProceedToStage(stage.id as BookStage) && stage.id !== currentStage;
              const isAccessible = canProceedToStage(stage.id as BookStage);
              
              return (
                <React.Fragment key={stage.id}>
                  <button
                    onClick={() => goToStage(stage.id as BookStage)}
                    disabled={!isAccessible}
                    className={`
                      flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all
                      ${isActive 
                        ? `bg-${stageColor}-100 text-${stageColor}-700 border border-${stageColor}-200` 
                        : isCompleted
                        ? 'bg-green-50 text-green-700 hover:bg-green-100'
                        : isAccessible
                        ? 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                        : 'bg-gray-50 text-gray-400 cursor-not-allowed'
                      }
                    `}
                  >
                    {isCompleted ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : (
                      <Icon className="h-4 w-4" />
                    )}
                    <span className="whitespace-nowrap">{stage.name}</span>
                  </button>
                  
                  {index < BOOK_STAGES.length - 1 && (
                    <ArrowRight className="h-4 w-4 text-gray-300 flex-shrink-0" />
                  )}
                </React.Fragment>
              );
            })}
          </div>
        </div>
      </div>

      {/* Main content area */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        {renderStageContent()}
      </div>

      {/* Footer with save status */}
      <div className="fixed bottom-4 right-4 z-40">
        <Card className="shadow-lg border-0 bg-white/90 backdrop-blur-sm">
          <CardContent className="p-3">
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>Auto-saved</span>
              <span className="text-gray-400">
                {new Date(lastSaved).toLocaleTimeString()}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
