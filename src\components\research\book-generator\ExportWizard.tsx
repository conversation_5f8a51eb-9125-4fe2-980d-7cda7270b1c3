import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Download } from "lucide-react";

interface ExportWizardProps {
  onBack: () => void;
}

export function ExportWizard({ onBack }: ExportWizardProps) {
  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <div className="p-3 bg-indigo-100 rounded-full">
            <Download className="h-8 w-8 text-indigo-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Export & Publish</h1>
            <p className="text-lg text-gray-600">Generate final formats for your book</p>
          </div>
        </div>
      </div>

      {/* Placeholder content */}
      <Card>
        <CardHeader>
          <CardTitle>Export Options</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-gray-500">
            <Download className="h-16 w-16 mx-auto mb-4 text-gray-300" />
            <p className="text-lg mb-2">Export Wizard</p>
            <p>PDF, EPUB, LaTeX, and Word export options will be available here</p>
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack} className="flex items-center gap-2">
          <ArrowLeft className="h-4 w-4" />
          Back to Editor
        </Button>
        <div className="flex gap-2">
          <Button variant="outline">
            Preview
          </Button>
          <Button>
            Export Book
          </Button>
        </div>
      </div>
    </div>
  );
}
