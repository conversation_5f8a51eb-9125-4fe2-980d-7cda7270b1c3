import { 
  BookOpen, 
  FileText, 
  Search, 
  Target, 
  BarChart3, 
  MessageSquare, 
  CheckCircle,
  Lightbulb,
  Users,
  Zap,
  Brain,
  Database
} from "lucide-react";
import { BookAIOptions } from './types';

// Default book generation stages
export const BOOK_STAGES = [
  { id: 'input', name: 'Book Setup', description: 'Define metadata and scope' },
  { id: 'outline', name: 'Outline Creation', description: 'Structure chapters and sections' },
  { id: 'chapterQueue', name: 'Content Generation', description: 'Generate chapter content' },
  { id: 'editor', name: 'Review & Edit', description: 'Refine and polish content' },
  { id: 'export', name: 'Export & Publish', description: 'Generate final formats' }
] as const;

// Common book section types for academic books
export const BOOK_SECTION_TYPES = [
  {
    id: 'introduction',
    name: 'Introduction',
    icon: Lightbulb,
    color: 'blue',
    description: 'Chapter introduction and overview',
    order: 1,
    estimatedWords: 800,
    required: true
  },
  {
    id: 'background',
    name: 'Background & Context',
    icon: Database,
    color: 'purple',
    description: 'Historical context and foundational concepts',
    order: 2,
    estimatedWords: 1200,
    required: false
  },
  {
    id: 'literature-review',
    name: 'Literature Review',
    icon: Search,
    color: 'green',
    description: 'Review of relevant research and publications',
    order: 3,
    estimatedWords: 1500,
    required: false
  },
  {
    id: 'methodology',
    name: 'Methodology',
    icon: Target,
    color: 'orange',
    description: 'Research methods and approaches',
    order: 4,
    estimatedWords: 1000,
    required: false
  },
  {
    id: 'analysis',
    name: 'Analysis & Discussion',
    icon: BarChart3,
    color: 'red',
    description: 'Detailed analysis and interpretation',
    order: 5,
    estimatedWords: 2000,
    required: false
  },
  {
    id: 'case-study',
    name: 'Case Study',
    icon: FileText,
    color: 'indigo',
    description: 'Practical examples and applications',
    order: 6,
    estimatedWords: 1500,
    required: false
  },
  {
    id: 'implications',
    name: 'Implications',
    icon: Brain,
    color: 'pink',
    description: 'Theoretical and practical implications',
    order: 7,
    estimatedWords: 800,
    required: false
  },
  {
    id: 'conclusion',
    name: 'Conclusion',
    icon: CheckCircle,
    color: 'emerald',
    description: 'Summary and future directions',
    order: 8,
    estimatedWords: 600,
    required: true
  }
];

// AI model options for book generation
export const BOOK_AI_MODELS = [
  {
    id: 'anthropic/claude-3.5-sonnet',
    name: 'Claude 3.5 Sonnet',
    provider: 'Anthropic',
    capabilities: ['long-form', 'academic', 'citations'],
    maxTokens: 8192,
    recommended: true,
    description: 'Best for academic writing and complex reasoning'
  },
  {
    id: 'openai/gpt-4-turbo',
    name: 'GPT-4 Turbo',
    provider: 'OpenAI',
    capabilities: ['long-form', 'creative', 'technical'],
    maxTokens: 4096,
    recommended: true,
    description: 'Excellent for technical and creative content'
  },
  {
    id: 'anthropic/claude-3-opus',
    name: 'Claude 3 Opus',
    provider: 'Anthropic',
    capabilities: ['long-form', 'academic', 'research'],
    maxTokens: 4096,
    recommended: false,
    description: 'High-quality academic writing'
  },
  {
    id: 'openai/gpt-4',
    name: 'GPT-4',
    provider: 'OpenAI',
    capabilities: ['balanced', 'reliable'],
    maxTokens: 8192,
    recommended: false,
    description: 'Reliable general-purpose model'
  }
];

// Default AI generation options
export const DEFAULT_AI_OPTIONS: BookAIOptions = {
  model: 'anthropic/claude-3.5-sonnet',
  temperature: 0.7,
  maxTokens: 4000,
  stopSequences: ['---', '###END###'],
  customInstructions: 'Write in an academic style suitable for graduate-level readers. Use clear, precise language and maintain scholarly tone throughout.'
};

// Book audience types
export const BOOK_AUDIENCES = [
  'Undergraduate students',
  'Graduate students', 
  'Researchers',
  'Academic professionals',
  'Industry professionals',
  'General educated public',
  'Specialists in the field',
  'Interdisciplinary audience'
];

// Writing style presets
export const WRITING_STYLES = [
  {
    id: 'academic',
    name: 'Academic',
    description: 'Formal scholarly writing with citations and technical precision',
    tone: 'formal',
    complexity: 'high'
  },
  {
    id: 'technical',
    name: 'Technical',
    description: 'Clear technical explanations with practical focus',
    tone: 'professional',
    complexity: 'high'
  },
  {
    id: 'accessible',
    name: 'Accessible Academic',
    description: 'Scholarly content made accessible to broader audiences',
    tone: 'approachable',
    complexity: 'medium'
  },
  {
    id: 'textbook',
    name: 'Textbook',
    description: 'Educational style with examples and clear explanations',
    tone: 'instructional',
    complexity: 'medium'
  },
  {
    id: 'popular',
    name: 'Popular Science',
    description: 'Engaging style for general educated readers',
    tone: 'engaging',
    complexity: 'low'
  }
];

// Citation styles for books
export const BOOK_CITATION_STYLES = [
  { id: 'apa', name: 'APA 7th Edition', description: 'American Psychological Association' },
  { id: 'mla', name: 'MLA 9th Edition', description: 'Modern Language Association' },
  { id: 'chicago', name: 'Chicago 17th Edition', description: 'Chicago Manual of Style' },
  { id: 'harvard', name: 'Harvard', description: 'Harvard referencing system' },
  { id: 'ieee', name: 'IEEE', description: 'Institute of Electrical and Electronics Engineers' },
  { id: 'vancouver', name: 'Vancouver', description: 'International Committee of Medical Journal Editors' }
];

// Export format options
export const EXPORT_FORMATS = [
  {
    id: 'pdf',
    name: 'PDF',
    description: 'Portable Document Format - best for reading and printing',
    icon: FileText,
    extensions: ['.pdf']
  },
  {
    id: 'epub',
    name: 'EPUB',
    description: 'Electronic Publication - for e-readers and mobile devices',
    icon: BookOpen,
    extensions: ['.epub']
  },
  {
    id: 'latex',
    name: 'LaTeX',
    description: 'LaTeX source files - for academic publishing',
    icon: FileText,
    extensions: ['.tex', '.zip']
  },
  {
    id: 'docx',
    name: 'Word Document',
    description: 'Microsoft Word format - for collaborative editing',
    icon: FileText,
    extensions: ['.docx']
  }
];

// Default export configuration
export const DEFAULT_EXPORT_CONFIG = {
  format: 'pdf' as const,
  includeGlossary: true,
  includeIndex: false,
  referencesMode: 'global' as const,
  figureQuality: 'print' as const,
  pageSize: 'a4' as const,
  margins: {
    top: 2.5,
    bottom: 2.5,
    left: 2.5,
    right: 2.5
  },
  fontFamily: 'Times New Roman',
  fontSize: 12,
  lineSpacing: 1.5,
  chapterBreaks: true
};

// Validation rules
export const VALIDATION_RULES = {
  minChapterLength: 2000,      // words
  maxChapterLength: 8000,      // words
  minSectionLength: 300,       // words
  maxSectionLength: 2000,      // words
  maxCitationsPerSection: 20,
  minCitationsPerChapter: 5,
  maxTermsInGlossary: 200,
  requiredSections: ['introduction', 'conclusion']
};

// UI configuration
export const UI_CONFIG = {
  maxRecentChapters: 5,
  maxSearchResults: 50,
  autoSaveInterval: 30000,     // 30 seconds
  maxUndoHistory: 50,
  defaultSidebarPanel: 'outline' as const,
  maxCitationPreview: 3,
  maxTermPreview: 5
};

// Generation queue priorities
export const QUEUE_PRIORITIES = {
  OUTLINE: 1,
  INTRODUCTION: 2,
  MAIN_CONTENT: 3,
  CONCLUSION: 4,
  REGENERATION: 5
};

// Status colors for UI
export const STATUS_COLORS = {
  draft: 'gray',
  'in-review': 'yellow',
  locked: 'green',
  pending: 'blue',
  generating: 'orange',
  completed: 'green',
  error: 'red',
  reviewing: 'purple'
} as const;
