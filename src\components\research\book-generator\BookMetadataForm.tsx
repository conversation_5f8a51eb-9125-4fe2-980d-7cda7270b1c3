import React, { useState } from 'react';
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  BookOpen, 
  Users, 
  Target, 
  FileText, 
  Plus, 
  X,
  ArrowRight,
  Lightbulb
} from "lucide-react";

import { useBookStore } from './hooks/useBookStore';
import { BOOK_AUDIENCES, WRITING_STYLES } from './constants';
import { BookMeta } from './types';

interface BookMetadataFormProps {
  onNext: () => void;
}

export function BookMetadataForm({ onNext }: BookMetadataFormProps) {
  const { metadata, setMetadata } = useBookStore();
  
  const [formData, setFormData] = useState<BookMeta>(metadata);
  const [newKeyword, setNewKeyword] = useState('');
  const [newAuthor, setNewAuthor] = useState('');

  const handleInputChange = (field: keyof BookMeta, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addKeyword = () => {
    if (newKeyword.trim() && !formData.keywords.includes(newKeyword.trim())) {
      handleInputChange('keywords', [...formData.keywords, newKeyword.trim()]);
      setNewKeyword('');
    }
  };

  const removeKeyword = (keyword: string) => {
    handleInputChange('keywords', formData.keywords.filter(k => k !== keyword));
  };

  const addAuthor = () => {
    if (newAuthor.trim() && !formData.authors.includes(newAuthor.trim())) {
      handleInputChange('authors', [...formData.authors, newAuthor.trim()]);
      setNewAuthor('');
    }
  };

  const removeAuthor = (author: string) => {
    handleInputChange('authors', formData.authors.filter(a => a !== author));
  };

  const handleSave = () => {
    // Validation
    if (!formData.title.trim()) {
      toast.error('Please enter a book title');
      return;
    }
    
    if (formData.authors.length === 0) {
      toast.error('Please add at least one author');
      return;
    }
    
    if (!formData.synopsis.trim()) {
      toast.error('Please provide a book synopsis');
      return;
    }

    if (!formData.audience) {
      toast.error('Please select a target audience');
      return;
    }

    if (!formData.styleSheet) {
      toast.error('Please select a writing style');
      return;
    }

    // Save to store
    setMetadata(formData);
    toast.success('Book metadata saved successfully');
    onNext();
  };

  const selectedStyle = WRITING_STYLES.find(s => s.id === formData.styleSheet);

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <div className="p-3 bg-blue-100 rounded-full">
            <BookOpen className="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Book Setup</h1>
            <p className="text-lg text-gray-600">Define your book's metadata and scope</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main form */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Basic Information
              </CardTitle>
              <CardDescription>
                Core details about your book
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <Label htmlFor="title">Book Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter your book title"
                    className="mt-1"
                  />
                </div>
                
                <div className="md:col-span-2">
                  <Label htmlFor="subtitle">Subtitle (Optional)</Label>
                  <Input
                    id="subtitle"
                    value={formData.subtitle || ''}
                    onChange={(e) => handleInputChange('subtitle', e.target.value)}
                    placeholder="Enter subtitle if applicable"
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="researchField">Research Field *</Label>
                  <Input
                    id="researchField"
                    value={formData.researchField}
                    onChange={(e) => handleInputChange('researchField', e.target.value)}
                    placeholder="e.g., Computer Science, Psychology"
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="targetLength">Target Length (words)</Label>
                  <Input
                    id="targetLength"
                    type="number"
                    value={formData.targetLength}
                    onChange={(e) => handleInputChange('targetLength', parseInt(e.target.value) || 50000)}
                    placeholder="50000"
                    className="mt-1"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="synopsis">Book Synopsis *</Label>
                <Textarea
                  id="synopsis"
                  value={formData.synopsis}
                  onChange={(e) => handleInputChange('synopsis', e.target.value)}
                  placeholder="Provide a comprehensive overview of your book's purpose, scope, and main themes..."
                  className="mt-1 min-h-[120px]"
                />
                <p className="text-xs text-gray-500 mt-1">
                  This synopsis will guide AI generation throughout the book
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Authors */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Authors
              </CardTitle>
              <CardDescription>
                Add all authors and contributors
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newAuthor}
                  onChange={(e) => setNewAuthor(e.target.value)}
                  placeholder="Enter author name"
                  onKeyPress={(e) => e.key === 'Enter' && addAuthor()}
                />
                <Button onClick={addAuthor} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="flex flex-wrap gap-2">
                {formData.authors.map((author, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {author}
                    <button
                      onClick={() => removeAuthor(author)}
                      className="ml-1 hover:text-red-600"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Keywords */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5" />
                Keywords
              </CardTitle>
              <CardDescription>
                Key terms and concepts covered in your book
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newKeyword}
                  onChange={(e) => setNewKeyword(e.target.value)}
                  placeholder="Enter keyword"
                  onKeyPress={(e) => e.key === 'Enter' && addKeyword()}
                />
                <Button onClick={addKeyword} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="flex flex-wrap gap-2">
                {formData.keywords.map((keyword, index) => (
                  <Badge key={index} variant="outline" className="flex items-center gap-1">
                    {keyword}
                    <button
                      onClick={() => removeKeyword(keyword)}
                      className="ml-1 hover:text-red-600"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Target Audience */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Target Audience
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Select
                value={formData.audience}
                onValueChange={(value) => handleInputChange('audience', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select audience" />
                </SelectTrigger>
                <SelectContent>
                  {BOOK_AUDIENCES.map((audience) => (
                    <SelectItem key={audience} value={audience}>
                      {audience}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          {/* Writing Style */}
          <Card>
            <CardHeader>
              <CardTitle>Writing Style</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Select
                value={formData.styleSheet}
                onValueChange={(value) => handleInputChange('styleSheet', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select style" />
                </SelectTrigger>
                <SelectContent>
                  {WRITING_STYLES.map((style) => (
                    <SelectItem key={style.id} value={style.id}>
                      {style.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {selectedStyle && (
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-700">{selectedStyle.description}</p>
                  <div className="flex gap-2 mt-2">
                    <Badge variant="outline" size="sm">
                      {selectedStyle.tone}
                    </Badge>
                    <Badge variant="outline" size="sm">
                      {selectedStyle.complexity} complexity
                    </Badge>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Progress Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Setup Progress</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Title</span>
                <span className={formData.title ? 'text-green-600' : 'text-gray-400'}>
                  {formData.title ? '✓' : '○'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Authors</span>
                <span className={formData.authors.length > 0 ? 'text-green-600' : 'text-gray-400'}>
                  {formData.authors.length > 0 ? '✓' : '○'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Synopsis</span>
                <span className={formData.synopsis ? 'text-green-600' : 'text-gray-400'}>
                  {formData.synopsis ? '✓' : '○'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Audience</span>
                <span className={formData.audience ? 'text-green-600' : 'text-gray-400'}>
                  {formData.audience ? '✓' : '○'}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Style</span>
                <span className={formData.styleSheet ? 'text-green-600' : 'text-gray-400'}>
                  {formData.styleSheet ? '✓' : '○'}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Action buttons */}
      <div className="flex justify-end">
        <Button onClick={handleSave} size="lg" className="flex items-center gap-2">
          Continue to Outline
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
